
// Show add tournament modal
function showAddTournamentModal() {
    $('#addTournamentModal').modal('show');
    // Auto-focus the native select when the modal is shown
    $('#addTournamentModal').on('shown.bs.modal', function () {
        $('#id_pais_modal').trigger('focus');
    });

    // Clear previous season input and validation
    $('#season_modal').val('');
    $('#season_validation_indicator').hide();

    // Set up real-time season validation
    setupSeasonValidation();
}

// Add selected tournament from modal
function addSelectedTournamentFromModal() {
    const selectedPaisId = document.getElementById('id_pais_modal').value;
    if (selectedPaisId) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'epartido_probabilidades';

        // Add hidden fields
        const idPartidoField = document.createElement('input');
        idPartidoField.type = 'hidden';
        idPartidoField.name = 'id_partido';
        idPartidoField.value = document.getElementById('id_partido').value;
        form.appendChild(idPartidoField);

        const tabSelectedField = document.createElement('input');
        tabSelectedField.type = 'hidden';
        tabSelectedField.name = 'tabselected';
        tabSelectedField.value = document.getElementById('tabselected').value;
        form.appendChild(tabSelectedField);

        const tabSelectedCornersField = document.createElement('input');
        tabSelectedCornersField.type = 'hidden';
        tabSelectedCornersField.name = 'tabselected_corners';
        tabSelectedCornersField.value = document.getElementById('tabselected_corners').value;
        form.appendChild(tabSelectedCornersField);

        const idPaisField = document.createElement('input');
        idPaisField.type = 'hidden';
        idPaisField.name = 'id_pais';
        idPaisField.value = selectedPaisId;
        form.appendChild(idPaisField);

        // Add season field if provided
        const seasonValue = document.getElementById('season_modal').value.trim();
        if (seasonValue) {
            const seasonField = document.createElement('input');
            seasonField.type = 'hidden';
            seasonField.name = 'season';
            seasonField.value = seasonValue;
            form.appendChild(seasonField);
        }

        document.body.appendChild(form);
        form.submit();
    } else {
        alert('Please select a tournament to add.');
    }
}

// Show create tournament modal
function showCreateTournamentModal() {
    $('#createTournamentModal').modal('show');
    // Auto-focus the tournament name field when modal is shown
    $('#createTournamentModal').on('shown.bs.modal', function () {
        $('#tournament_name_create').focus();
    });
}

// Show edit tournament modal
function openEditTournamentModal(tournamentId, tournamentName) {
    document.getElementById('tournament_id_edit').value = tournamentId;
    document.getElementById('tournament_name_edit').value = tournamentName;
    $('#editTournamentModal').modal('show');
    // Auto-focus the tournament name field when modal is shown
    $('#editTournamentModal').on('shown.bs.modal', function () {
        $('#tournament_name_edit').focus().select();
    });
}

// Handle create tournament form submission
document.getElementById('createTournamentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('sub_create_tournament', '1');
    formData.append('tournament_name', document.getElementById('tournament_name_create').value);
    formData.append('id_partido', document.getElementById('id_partido').value);
    formData.append('tabselected', document.getElementById('tabselected').value);
    
    fetch('epartido_probabilidades', {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            // Close the modal and reload the page
            $('#createTournamentModal').modal('hide');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error creating tournament. Please try again.');
        });
});

// Handle edit tournament form submission
document.getElementById('editTournamentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    formData.append('sub_edit_tournament', '1');
    formData.append('tournament_id', document.getElementById('tournament_id_edit').value);
    formData.append('tournament_name', document.getElementById('tournament_name_edit').value);
    formData.append('id_partido', document.getElementById('id_partido').value);
    formData.append('tabselected', document.getElementById('tabselected').value);
    
    fetch('epartido_probabilidades', {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            // Close the modal and reload the page
            $('#editTournamentModal').modal('hide');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error updating tournament. Please try again.');
        });
});

// Setup real-time season validation
function setupSeasonValidation() {
    let validationTimeout;

    $('#season_modal').on('input', function() {
        const seasonValue = $(this).val().trim();
        const paisId = $('#id_pais_modal').val();

        // Clear previous timeout
        clearTimeout(validationTimeout);

        // Hide validation indicator if less than 4 characters or no country selected
        if (seasonValue.length < 4 || !paisId) {
            $('#season_validation_indicator').hide();
            return;
        }

        // Set timeout for validation (debounce)
        validationTimeout = setTimeout(function() {
            validateSeasonWithAPI(paisId, seasonValue);
        }, 500); // Wait 500ms after user stops typing
    });

    // Also validate when country selection changes
    $('#id_pais_modal').on('change', function() {
        const seasonValue = $('#season_modal').val().trim();
        const paisId = $(this).val();

        if (seasonValue.length >= 4 && paisId) {
            validateSeasonWithAPI(paisId, seasonValue);
        } else {
            $('#season_validation_indicator').hide();
        }
    });
}

// Validate season with API
function validateSeasonWithAPI(paisId, season) {
    // Show loading indicator
    $('#season_validation_indicator').show();
    $('#season_validation_content').html('<i class="fas fa-spinner fa-spin text-info me-2"></i><small class="text-info">Validating season...</small>');

    // Make AJAX call to validate
    $.ajax({
        url: 'epartido_probabilidades',
        method: 'POST',
        dataType: 'json',
        data: {
            ajax_validate_season: 1,
            pais_id: paisId,
            season: season
        },
        success: function(response) {
            if (response.status === 'success') {
                if (response.exists) {
                    // Record exists - show green checkmark
                    $('#season_validation_content').html('<i class="fas fa-check-circle text-success me-2"></i><small class="text-success">Season found in Footy API (ID: ' + response.footy_id + ')</small>');
                } else {
                    // Record doesn't exist - show warning
                    $('#season_validation_content').html('<i class="fas fa-exclamation-triangle text-warning me-2"></i><small class="text-warning">Season not found in Footy API</small>');
                }
            } else {
                // Error occurred
                $('#season_validation_content').html('<i class="fas fa-times-circle text-danger me-2"></i><small class="text-danger">Error validating season</small>');
            }
        },
        error: function() {
            $('#season_validation_content').html('<i class="fas fa-times-circle text-danger me-2"></i><small class="text-danger">Error validating season</small>');
        }
    });
}

// Historical filtering functionality will be handled inline in the PHP view file
// since it needs access to PHP variables for team names
