-- Add season column to partidos_torneos table
-- This script adds support for season-specific tournament associations

-- Check if the column doesn't exist before adding it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'partidos_torneos'
    AND COLUMN_NAME = 'season'
);

-- Add the column only if it doesn't exist
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE partidos_torneos ADD COLUMN season VARCHAR(20) NULL AFTER id_pais',
    'SELECT "Column season already exists in partidos_torneos table" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Show the result
SELECT 
    CASE 
        WHEN @column_exists = 0 THEN 'Season column added successfully to partidos_torneos table'
        ELSE 'Season column already exists in partidos_torneos table'
    END as result;
