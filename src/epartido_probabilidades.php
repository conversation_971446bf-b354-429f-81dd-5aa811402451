<?php session_start();

/** @var PDO $conexion */
global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/partidotorneo.php';
require_once __ROOT__ . '/src/classes/partidoprobabilidad.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/classes/partidoinfo.php';
require_once __ROOT__ . '/src/classes/paisseason.php';
require_once __ROOT__ . '/src/classes/PaisesTorneoFootyApi.php';
require_once __ROOT__ . '/src/classes/PartidoBetDetalle.php';
require_once __ROOT__ . '/src/classes/PartidoBetCriterio.php';
require_once __ROOT__ . '/src/general/preparar.php';

$id_partido               = '';
$tabselected              = 1; // Default to total superior 1.5 tab (goals section)
$tabselected_corners      = 7; // Default to 6.5 tab (corners section)
$n_row_partidos_infos     = 1;
$partidos_infos           = array();
$partidos_torneos_grouped = array();
$success_display          = '';
$success_text             = '';
$error_display            = '';
$error_text               = '';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		// Check for id_partido in URL parameter first (for redirects after AJAX)
		if (isset($_GET['id_partido']) && !empty($_GET['id_partido'])) {
			$id_partido = limpiar_datos($_GET['id_partido']);
			// Store in session for consistency
			$_SESSION['id_partido'] = $id_partido;
		} elseif (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
			// Don't unset the session variable to allow page reloads
		} else {
			header('Location: lpartidos');
			exit();
		}
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		$id_partido          = isset($_POST['id_partido']) ? limpiar_datos($_POST['id_partido']) : '';
		$id_pais             = isset($_POST['id_pais']) ? limpiar_datos($_POST['id_pais']) : '';
		$season              = isset($_POST['season']) ? limpiar_datos($_POST['season']) : '';
		$tabselected         = isset($_POST['tabselected']) ? limpiar_datos($_POST["tabselected"]) : 1;
		$tabselected_corners = isset($_POST['tabselected_corners']) ? limpiar_datos($_POST["tabselected_corners"]) : 7;

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion postsolo
#region sub_marcar_revisado_probabilidades
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_marcar_revisado_probabilidades'])) {
	try {
		Partido::modify_marcar_revisado_probabilidades($id_partido, $conexion);
		
		$next_id_partido = Partido::get_next_por_revisar_probabilidades($conexion);
		
		if (!empty($next_id_partido)) {
			$_SESSION['id_partido'] = $next_id_partido;
			
			header('Location: epartido_probabilidades');
		} else {
			header('Location: lpartidos?nnpp=1');
		}
		exit();
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_marcar_revisado_probabilidades
#region sub_upload_torneo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_upload_torneo'])) {
	try {
		$method_sub_upload_torneo = 1;

		$conexion->beginTransaction();

		$pais_upload_id = limpiar_datos($_POST['pais_upload_id']);
		$season_upload = limpiar_datos($_POST['season_upload']);
		$archivo = $_FILES['archivocsv']['tmp_name'];
		$archivocsv = file($archivo);

		$param = array();
		$param['archivocsv'] = $archivocsv;
		$param['season'] = $season_upload;
		$param['idpais'] = $pais_upload_id;
		PartidoInfo::deleteSelected($param, $conexion);
		PartidoInfo::uploadCSV($param, $conexion);

		$nom_pais_upload = Pais::get($pais_upload_id, $conexion)->nombre;

		$param = array();
		$param['nom_pais'] = $nom_pais_upload;
		$param['season'] = $season_upload;
		PartidoInfo::add_pais_season($param, $conexion);

		$conexion->commit();

		$success_display = 'show';
		$success_text = 'Los datos del torneo han sido cargados exitosamente.';

	} catch (Exception $e) {
		$conexion->rollback();

		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_upload_torneo
#region sub_actualizar_via_api
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_actualizar_via_api'])) {
	// Return JSON response for AJAX
	header('Content-Type: application/json');

	try {
		$pais_id = limpiar_datos($_POST['pais_id']);
		$season = limpiar_datos($_POST['season']);
		$footy_id = limpiar_datos($_POST['footy_id']);

		// Validate inputs
		if (empty($pais_id) || empty($season) || empty($footy_id)) {
			throw new Exception('Faltan parámetros requeridos');
		}

		$conexion->beginTransaction();

		// Delete existing records for this country and season before importing new ones
		$delete_params = array();
		$delete_params['idpais'] = $pais_id;
		$delete_params['season'] = $season;
		PartidoInfo::deleteSelected($delete_params, $conexion);

		// Set Bogotá timezone for fecha_upload
		setTimeZoneCol();
		$fecha_upload = create_date();

		$records_created = 0;
		$page = 1;
		$api_key = FTS_API;

		do {
			// Build API URL with pagination
			$api_url = "https://api.football-data-api.com/league-matches?key={$api_key}&season_id={$footy_id}&max_per_page=1000&page={$page}";

			// Make API call
			$response = file_get_contents($api_url);
			if ($response === false) {
				throw new Exception('Error al conectar con la API');
			}

			$data = json_decode($response, true);
			if (!$data || !isset($data['success']) || !$data['success']) {
				throw new Exception('Respuesta inválida de la API');
			}

			// Check if we have data
			if ($data['data'] === null) {
				break; // No more data, exit loop
			}

			// Process each match
			foreach ($data['data'] as $match) {
				// Only save records where status = "complete"
				if (strtolower($match['status']) !== 'complete') {
					continue;
				}

				// Create new PartidoInfo record
				$newpartidoinfo = new PartidoInfo();

				// Convert Unix timestamp to yyyy-MM-dd format
				$newpartidoinfo->fecha = date('Y-m-d', $match['date_unix']);
				$newpartidoinfo->status = $match['status'];
				$newpartidoinfo->home = $match['home_name'];
				$newpartidoinfo->away = $match['away_name'];
				$newpartidoinfo->homegoals = (int)$match['homeGoalCount'];
				$newpartidoinfo->awaygoals = (int)$match['awayGoalCount'];
				$newpartidoinfo->homecorners = (int)$match['team_a_corners'];
				$newpartidoinfo->awaycorners = (int)$match['team_b_corners'];
				$newpartidoinfo->homeshots = (int)$match['team_a_shots'];
				$newpartidoinfo->awayshots = (int)$match['team_b_shots'];
				$newpartidoinfo->homeshotstarget = (int)$match['team_a_shotsOnTarget'];
				$newpartidoinfo->awayshotstarget = (int)$match['team_b_shotsOnTarget'];
				$newpartidoinfo->homeposession = (int)$match['team_a_possession'];
				$newpartidoinfo->awayposession = (int)$match['team_b_possession'];
				$newpartidoinfo->pais = new Pais();
				$newpartidoinfo->pais->id = $pais_id;
				$newpartidoinfo->season = $season;
				$newpartidoinfo->fecha_upload = $fecha_upload;

				$newpartidoinfo->add($conexion);
				$records_created++;
			}

			$page++;

		} while ($data['data'] !== null);

		// Update pais season
		$nom_pais = Pais::get($pais_id, $conexion)->nombre;
		$param = array();
		$param['nom_pais'] = $nom_pais;
		$param['season'] = $season;
		PartidoInfo::add_pais_season($param, $conexion);

		$conexion->commit();

		echo json_encode([
			'status' => 'success',
			'message' => 'Datos actualizados exitosamente via API',
			'records_created' => $records_created
		]);
		exit();

	} catch (Exception $e) {
		$conexion->rollback();

		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion sub_actualizar_via_api
#region ajax_validate_season
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_validate_season'])) {
	// Return JSON response for AJAX
	header('Content-Type: application/json');

	try {
		$pais_id = limpiar_datos($_POST['pais_id']);
		$season = limpiar_datos($_POST['season']);

		// Validate inputs
		if (empty($pais_id) || empty($season)) {
			throw new Exception('Missing required parameters');
		}

		// Check if record exists in PaisesTorneoFootyApi
		$footy_api_record = \App\classes\PaisesTorneoFootyApi::getByPaisAndSeason(
			(int)ordena($pais_id),
			$season,
			$conexion
		);

		if ($footy_api_record) {
			echo json_encode([
				'status' => 'success',
				'exists' => true,
				'footy_id' => $footy_api_record->getIdFooty()
			]);
		} else {
			echo json_encode([
				'status' => 'success',
				'exists' => false
			]);
		}
		exit();

	} catch (Exception $e) {
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_validate_season
#region try
try {
	$update_partido_torneos = 0;
	$cur_partido            = Partido::get($id_partido, $conexion);
	$paises_torneos_info    = PartidoInfo::get_list_byhome_n_away(array('home' => $cur_partido->home, 'away' => $cur_partido->away), $conexion);
	$numero_por_revisar     = Partido::get_numero_por_revisar_probabilidades(array(), $conexion);
	
	//guardar en tabla partidos_torneos si aun no se ha agregado el torneo del partido.
	if (PartidoTorneo::existe_by_idpais_n_idpartido($cur_partido->pais_torneo->id, $id_partido, $conexion) == 0) {
		$new_partido_torneo              = new PartidoTorneo();
		$new_partido_torneo->partido->id = $id_partido;
		$new_partido_torneo->pais->id    = $cur_partido->pais_torneo->id;
		$new_partido_torneo->add($conexion);
		
		$update_partido_torneos = 1;
	}
	
	//guardar torneo seleccionado en el select de pais-torneo
	if (!empty($id_pais)) {
		$new_partido_torneo              = new PartidoTorneo();
		$new_partido_torneo->partido->id = $id_partido;
		$new_partido_torneo->pais->id    = $id_pais;

		// Set season for validation purposes (season data comes from partidos_info)
		if (!empty($season)) {
			$new_partido_torneo->season = $season;
		}

		$new_partido_torneo->add($conexion);

		$update_partido_torneos = 1;
	}
	
	//actualizar tabla de torneos
	$partido_torneos          = PartidoTorneo::getList($id_partido, $conexion);
	$partidos_torneos_grouped = PartidoTorneo::get_list_grouped_w_info($id_partido, $conexion);

	// Enrich tournament data with Footy IDs and date calculations for view
	foreach ($partidos_torneos_grouped as $partido_torneo) {
		// Get Footy ID for this tournament
		$partido_torneo->footy_id = '';
		try {
			$footy_api_record = \App\classes\PaisesTorneoFootyApi::getByPaisAndSeason(
				(int)ordena($partido_torneo->pais->id),
				$partido_torneo->season,
				$conexion
			);
			if ($footy_api_record) {
				$partido_torneo->footy_id = $footy_api_record->getIdFooty();
			}
		} catch (Exception $e) {
			// Log error but don't break the page
			error_log("Error getting Footy ID: " . $e->getMessage());
		}

		// Calculate date-related properties for display
		$partido_torneo->text_color_class = '';
		$partido_torneo->days_diff = null;
		if (!empty($partido_torneo->fecha_upload)) {
			setTimeZoneCol(); // Set Bogotá timezone
			$fecha_actual = create_date();
			$partido_torneo->days_diff = getDateDiffDaysNonLiteral($partido_torneo->fecha_upload, $fecha_actual);

			// Apply color logic: green for 0-6 days, gray for 7+ days
			if ($partido_torneo->days_diff >= 0 && $partido_torneo->days_diff <= 6) {
				$partido_torneo->text_color_class = 'text-success';
			} else {
				$partido_torneo->text_color_class = 'text-muted';
			}
		}
	}
	
	//obtener historico de partidos
	$param                       = array();
	$param['home']               = $cur_partido->home;
	$param['away']               = $cur_partido->away;
	$param['paises_adicionales'] = $partidos_torneos_grouped;
	$param['orderby_fecha']      = 1;
	$partidos_infos              = PartidoInfo::getList($param, $conexion);
	
	//analizar total goles mas de 1.5
	$min_porc_viable                       = 70;
	$n_partidos_p1                         = 5;
	$n_partidos_p2                         = 10;
	$n_partidos_home_h                     = 0;
	$n_partidos_away_a                     = 0;
	$n_partidos_home                       = 0;
	$n_partidos_away                       = 0;
	$n_cumple_total_goles_masde_1_5_home_h = 0;
	$n_cumple_total_goles_masde_1_5_away_a = 0;
	$n_cumple_total_goles_masde_1_5_home   = 0;
	$n_cumple_total_goles_masde_1_5_away   = 0;
	$prob_total_goles_masde_1_5_p1_home_h  = -1;
	$prob_total_goles_masde_1_5_p2_home_h  = -1;
	$prob_total_goles_masde_1_5_p1_away_a  = -1;
	$prob_total_goles_masde_1_5_p2_away_a  = -1;
	$prob_total_goles_masde_1_5_p1_home    = -1;
	$prob_total_goles_masde_1_5_p2_home    = -1;
	$prob_total_goles_masde_1_5_p1_away    = -1;
	$prob_total_goles_masde_1_5_p2_away    = -1;
	$n_cumple_total_corners_masde_6_5_home_h = 0;
	$n_cumple_total_corners_masde_6_5_away_a = 0;
	$n_cumple_total_corners_masde_6_5_home   = 0;
	$n_cumple_total_corners_masde_6_5_away   = 0;
	$prob_total_corners_masde_6_5_p1_home_h  = -1;
	$prob_total_corners_masde_6_5_p2_home_h  = -1;
	$prob_total_corners_masde_6_5_p1_away_a  = -1;
	$prob_total_corners_masde_6_5_p2_away_a  = -1;
	$prob_total_corners_masde_6_5_p1_home    = -1;
	$prob_total_corners_masde_6_5_p2_home    = -1;
	$prob_total_corners_masde_6_5_p1_away    = -1;
	$prob_total_corners_masde_6_5_p2_away    = -1;
	// 7.5 corners variables
	$n_cumple_total_corners_masde_7_5_home_h = 0;
	$n_cumple_total_corners_masde_7_5_away_a = 0;
	$n_cumple_total_corners_masde_7_5_home   = 0;
	$n_cumple_total_corners_masde_7_5_away   = 0;
	$prob_total_corners_masde_7_5_p1_home_h  = -1;
	$prob_total_corners_masde_7_5_p2_home_h  = -1;
	$prob_total_corners_masde_7_5_p1_away_a  = -1;
	$prob_total_corners_masde_7_5_p2_away_a  = -1;
	$prob_total_corners_masde_7_5_p1_home    = -1;
	$prob_total_corners_masde_7_5_p2_home    = -1;
	$prob_total_corners_masde_7_5_p1_away    = -1;
	$prob_total_corners_masde_7_5_p2_away    = -1;
	// 8.5 corners variables
	$n_cumple_total_corners_masde_8_5_home_h = 0;
	$n_cumple_total_corners_masde_8_5_away_a = 0;
	$n_cumple_total_corners_masde_8_5_home   = 0;
	$n_cumple_total_corners_masde_8_5_away   = 0;
	$prob_total_corners_masde_8_5_p1_home_h  = -1;
	$prob_total_corners_masde_8_5_p2_home_h  = -1;
	$prob_total_corners_masde_8_5_p1_away_a  = -1;
	$prob_total_corners_masde_8_5_p2_away_a  = -1;
	$prob_total_corners_masde_8_5_p1_home    = -1;
	$prob_total_corners_masde_8_5_p2_home    = -1;
	$prob_total_corners_masde_8_5_p1_away    = -1;
	$prob_total_corners_masde_8_5_p2_away    = -1;
	// 9.5 corners variables
	$n_cumple_total_corners_masde_9_5_home_h = 0;
	$n_cumple_total_corners_masde_9_5_away_a = 0;
	$n_cumple_total_corners_masde_9_5_home   = 0;
	$n_cumple_total_corners_masde_9_5_away   = 0;
	$prob_total_corners_masde_9_5_p1_home_h  = -1;
	$prob_total_corners_masde_9_5_p2_home_h  = -1;
	$prob_total_corners_masde_9_5_p1_away_a  = -1;
	$prob_total_corners_masde_9_5_p2_away_a  = -1;
	$prob_total_corners_masde_9_5_p1_home    = -1;
	$prob_total_corners_masde_9_5_p2_home    = -1;
	$prob_total_corners_masde_9_5_p1_away    = -1;
	$prob_total_corners_masde_9_5_p2_away    = -1;
	// 10.5 corners variables
	$n_cumple_total_corners_masde_10_5_home_h = 0;
	$n_cumple_total_corners_masde_10_5_away_a = 0;
	$n_cumple_total_corners_masde_10_5_home   = 0;
	$n_cumple_total_corners_masde_10_5_away   = 0;
	$prob_total_corners_masde_10_5_p1_home_h  = -1;
	$prob_total_corners_masde_10_5_p2_home_h  = -1;
	$prob_total_corners_masde_10_5_p1_away_a  = -1;
	$prob_total_corners_masde_10_5_p2_away_a  = -1;
	$prob_total_corners_masde_10_5_p1_home    = -1;
	$prob_total_corners_masde_10_5_p2_home    = -1;
	$prob_total_corners_masde_10_5_p1_away    = -1;
	$prob_total_corners_masde_10_5_p2_away    = -1;
	// 11.5 corners variables
	$n_cumple_total_corners_masde_11_5_home_h = 0;
	$n_cumple_total_corners_masde_11_5_away_a = 0;
	$n_cumple_total_corners_masde_11_5_home   = 0;
	$n_cumple_total_corners_masde_11_5_away   = 0;
	$prob_total_corners_masde_11_5_p1_home_h  = -1;
	$prob_total_corners_masde_11_5_p2_home_h  = -1;
	$prob_total_corners_masde_11_5_p1_away_a  = -1;
	$prob_total_corners_masde_11_5_p2_away_a  = -1;
	$prob_total_corners_masde_11_5_p1_home    = -1;
	$prob_total_corners_masde_11_5_p2_home    = -1;
	$prob_total_corners_masde_11_5_p1_away    = -1;
	$prob_total_corners_masde_11_5_p2_away    = -1;
	// 12.5 corners variables
	$n_cumple_total_corners_masde_12_5_home_h = 0;
	$n_cumple_total_corners_masde_12_5_away_a = 0;
	$n_cumple_total_corners_masde_12_5_home   = 0;
	$n_cumple_total_corners_masde_12_5_away   = 0;
	$prob_total_corners_masde_12_5_p1_home_h  = -1;
	$prob_total_corners_masde_12_5_p2_home_h  = -1;
	$prob_total_corners_masde_12_5_p1_away_a  = -1;
	$prob_total_corners_masde_12_5_p2_away_a  = -1;
	$prob_total_corners_masde_12_5_p1_home    = -1;
	$prob_total_corners_masde_12_5_p2_home    = -1;
	$prob_total_corners_masde_12_5_p1_away    = -1;
	$prob_total_corners_masde_12_5_p2_away    = -1;
	// 13.5 corners variables
	$n_cumple_total_corners_masde_13_5_home_h = 0;
	$n_cumple_total_corners_masde_13_5_away_a = 0;
	$n_cumple_total_corners_masde_13_5_home   = 0;
	$n_cumple_total_corners_masde_13_5_away   = 0;
	$prob_total_corners_masde_13_5_p1_home_h  = -1;
	$prob_total_corners_masde_13_5_p2_home_h  = -1;
	$prob_total_corners_masde_13_5_p1_away_a  = -1;
	$prob_total_corners_masde_13_5_p2_away_a  = -1;
	$prob_total_corners_masde_13_5_p1_home    = -1;
	$prob_total_corners_masde_13_5_p2_home    = -1;
	$prob_total_corners_masde_13_5_p1_away    = -1;
	$prob_total_corners_masde_13_5_p2_away    = -1;

	// INFERIOR CORNERS VARIABLES - for "Corners Totales Inferior" section
	// 6.5 corners inferior variables
	$n_cumple_total_corners_menosde_6_5_home_h = 0;
	$n_cumple_total_corners_menosde_6_5_away_a = 0;
	$n_cumple_total_corners_menosde_6_5_home   = 0;
	$n_cumple_total_corners_menosde_6_5_away   = 0;
	$prob_total_corners_menosde_6_5_p1_home_h  = -1;
	$prob_total_corners_menosde_6_5_p2_home_h  = -1;
	$prob_total_corners_menosde_6_5_p1_away_a  = -1;
	$prob_total_corners_menosde_6_5_p2_away_a  = -1;
	$prob_total_corners_menosde_6_5_p1_home    = -1;
	$prob_total_corners_menosde_6_5_p2_home    = -1;
	$prob_total_corners_menosde_6_5_p1_away    = -1;
	$prob_total_corners_menosde_6_5_p2_away    = -1;
	// 7.5 corners inferior variables
	$n_cumple_total_corners_menosde_7_5_home_h = 0;
	$n_cumple_total_corners_menosde_7_5_away_a = 0;
	$n_cumple_total_corners_menosde_7_5_home   = 0;
	$n_cumple_total_corners_menosde_7_5_away   = 0;
	$prob_total_corners_menosde_7_5_p1_home_h  = -1;
	$prob_total_corners_menosde_7_5_p2_home_h  = -1;
	$prob_total_corners_menosde_7_5_p1_away_a  = -1;
	$prob_total_corners_menosde_7_5_p2_away_a  = -1;
	$prob_total_corners_menosde_7_5_p1_home    = -1;
	$prob_total_corners_menosde_7_5_p2_home    = -1;
	$prob_total_corners_menosde_7_5_p1_away    = -1;
	$prob_total_corners_menosde_7_5_p2_away    = -1;
	// 8.5 corners inferior variables
	$n_cumple_total_corners_menosde_8_5_home_h = 0;
	$n_cumple_total_corners_menosde_8_5_away_a = 0;
	$n_cumple_total_corners_menosde_8_5_home   = 0;
	$n_cumple_total_corners_menosde_8_5_away   = 0;
	$prob_total_corners_menosde_8_5_p1_home_h  = -1;
	$prob_total_corners_menosde_8_5_p2_home_h  = -1;
	$prob_total_corners_menosde_8_5_p1_away_a  = -1;
	$prob_total_corners_menosde_8_5_p2_away_a  = -1;
	$prob_total_corners_menosde_8_5_p1_home    = -1;
	$prob_total_corners_menosde_8_5_p2_home    = -1;
	$prob_total_corners_menosde_8_5_p1_away    = -1;
	$prob_total_corners_menosde_8_5_p2_away    = -1;
	// 9.5 corners inferior variables
	$n_cumple_total_corners_menosde_9_5_home_h = 0;
	$n_cumple_total_corners_menosde_9_5_away_a = 0;
	$n_cumple_total_corners_menosde_9_5_home   = 0;
	$n_cumple_total_corners_menosde_9_5_away   = 0;
	$prob_total_corners_menosde_9_5_p1_home_h  = -1;
	$prob_total_corners_menosde_9_5_p2_home_h  = -1;
	$prob_total_corners_menosde_9_5_p1_away_a  = -1;
	$prob_total_corners_menosde_9_5_p2_away_a  = -1;
	$prob_total_corners_menosde_9_5_p1_home    = -1;
	$prob_total_corners_menosde_9_5_p2_home    = -1;
	$prob_total_corners_menosde_9_5_p1_away    = -1;
	$prob_total_corners_menosde_9_5_p2_away    = -1;
	// 10.5 corners inferior variables
	$n_cumple_total_corners_menosde_10_5_home_h = 0;
	$n_cumple_total_corners_menosde_10_5_away_a = 0;
	$n_cumple_total_corners_menosde_10_5_home   = 0;
	$n_cumple_total_corners_menosde_10_5_away   = 0;
	$prob_total_corners_menosde_10_5_p1_home_h  = -1;
	$prob_total_corners_menosde_10_5_p2_home_h  = -1;
	$prob_total_corners_menosde_10_5_p1_away_a  = -1;
	$prob_total_corners_menosde_10_5_p2_away_a  = -1;
	$prob_total_corners_menosde_10_5_p1_home    = -1;
	$prob_total_corners_menosde_10_5_p2_home    = -1;
	$prob_total_corners_menosde_10_5_p1_away    = -1;
	$prob_total_corners_menosde_10_5_p2_away    = -1;
	// 11.5 corners inferior variables
	$n_cumple_total_corners_menosde_11_5_home_h = 0;
	$n_cumple_total_corners_menosde_11_5_away_a = 0;
	$n_cumple_total_corners_menosde_11_5_home   = 0;
	$n_cumple_total_corners_menosde_11_5_away   = 0;
	$prob_total_corners_menosde_11_5_p1_home_h  = -1;
	$prob_total_corners_menosde_11_5_p2_home_h  = -1;
	$prob_total_corners_menosde_11_5_p1_away_a  = -1;
	$prob_total_corners_menosde_11_5_p2_away_a  = -1;
	$prob_total_corners_menosde_11_5_p1_home    = -1;
	$prob_total_corners_menosde_11_5_p2_home    = -1;
	$prob_total_corners_menosde_11_5_p1_away    = -1;
	$prob_total_corners_menosde_11_5_p2_away    = -1;
	// 12.5 corners inferior variables
	$n_cumple_total_corners_menosde_12_5_home_h = 0;
	$n_cumple_total_corners_menosde_12_5_away_a = 0;
	$n_cumple_total_corners_menosde_12_5_home   = 0;
	$n_cumple_total_corners_menosde_12_5_away   = 0;
	$prob_total_corners_menosde_12_5_p1_home_h  = -1;
	$prob_total_corners_menosde_12_5_p2_home_h  = -1;
	$prob_total_corners_menosde_12_5_p1_away_a  = -1;
	$prob_total_corners_menosde_12_5_p2_away_a  = -1;
	$prob_total_corners_menosde_12_5_p1_home    = -1;
	$prob_total_corners_menosde_12_5_p2_home    = -1;
	$prob_total_corners_menosde_12_5_p1_away    = -1;
	$prob_total_corners_menosde_12_5_p2_away    = -1;
	// 13.5 corners inferior variables
	$n_cumple_total_corners_menosde_13_5_home_h = 0;
	$n_cumple_total_corners_menosde_13_5_away_a = 0;
	$n_cumple_total_corners_menosde_13_5_home   = 0;
	$n_cumple_total_corners_menosde_13_5_away   = 0;
	$prob_total_corners_menosde_13_5_p1_home_h  = -1;
	$prob_total_corners_menosde_13_5_p2_home_h  = -1;
	$prob_total_corners_menosde_13_5_p1_away_a  = -1;
	$prob_total_corners_menosde_13_5_p2_away_a  = -1;
	$prob_total_corners_menosde_13_5_p1_home    = -1;
	$prob_total_corners_menosde_13_5_p2_home    = -1;
	$prob_total_corners_menosde_13_5_p1_away    = -1;
	$prob_total_corners_menosde_13_5_p2_away    = -1;
	$n_cumple_home_marca_home_h            = 0;
	$n_cumple_home_marca_home              = 0;
	$prob_home_marca_p1_home_h             = -1;
	$prob_home_marca_p2_home_h             = -1;
	$prob_home_marca_p1_home               = -1;
	$prob_home_marca_p2_home               = -1;
	$n_cumple_home_superior_1_5_home_h     = 0;
	$n_cumple_home_superior_1_5_home       = 0;
	$prob_home_superior_1_5_p1_home_h      = -1;
	$prob_home_superior_1_5_p2_home_h      = -1;
	$prob_home_superior_1_5_p1_home        = -1;
	$prob_home_superior_1_5_p2_home        = -1;
	$n_cumple_away_marca_away_a            = 0;
	$n_cumple_away_marca_away              = 0;
	$prob_away_marca_p1_away_a             = -1;
	$prob_away_marca_p2_away_a             = -1;
	$prob_away_marca_p1_away               = -1;
	$prob_away_marca_p2_away               = -1;
	$n_cumple_away_superior_1_5_away_a     = 0;
	$n_cumple_away_superior_1_5_away       = 0;
	$prob_away_superior_1_5_p1_away_a      = -1;
	$prob_away_superior_1_5_p2_away_a      = -1;
	$prob_away_superior_1_5_p1_away        = -1;
	$prob_away_superior_1_5_p2_away        = -1;
	$n_cumple_ambos_marcan_home_h          = 0;
	$n_cumple_ambos_marcan_away_a          = 0;
	$n_cumple_ambos_marcan_home            = 0;
	$n_cumple_ambos_marcan_away            = 0;
	$prob_ambos_marcan_p1_home_h           = -1;
	$prob_ambos_marcan_p2_home_h           = -1;
	$prob_ambos_marcan_p1_away_a           = -1;
	$prob_ambos_marcan_p2_away_a           = -1;
	$prob_ambos_marcan_p1_home             = -1;
	$prob_ambos_marcan_p2_home             = -1;
	$prob_ambos_marcan_p1_away             = -1;
	$prob_ambos_marcan_p2_away             = -1;
	$total_goals_home                      = 0;
	$total_goals_away                      = 0;
	$avg_goals_p1_home                     = -1;
	$avg_goals_p2_home                     = -1;
	$avg_goals_p1_away                     = -1;
	$avg_goals_p2_away                     = -1;
	$total_conceded_home                   = 0;
	$total_conceded_away                   = 0;
	$avg_conceded_p1_home                  = -1;
	$avg_conceded_p2_home                  = -1;
	$avg_conceded_p1_away                  = -1;
	$avg_conceded_p2_away                  = -1;
	$total_corners_home                    = 0;
	$total_corners_away                    = 0;
	$avg_corners_p1_home                   = -1;
	$avg_corners_p2_home                   = -1;
	$avg_corners_p1_away                   = -1;
	$avg_corners_p2_away                   = -1;
	$total_total_corners_home              = 0;
	$total_total_corners_away              = 0;
	$avg_total_corners_p1_home             = -1;
	$avg_total_corners_p2_home             = -1;
	$avg_total_corners_p1_away             = -1;
	$avg_total_corners_p2_away             = -1;
	
	/** @var PartidoInfo[] $partidos_infos */
	foreach ($partidos_infos as $partido_info) {
		$total_goles = $partido_info->homegoals + $partido_info->awaygoals;
		$total_corners = $partido_info->homecorners + $partido_info->awaycorners;

		//sum goals to each team so that we get the avg per number of matches
		//sum conceded goals to each team so that we get the avg per number of matches
		//sum total corners (home + away combined) for each team's matches
		if ($cur_partido->home == $partido_info->home) {
			$total_goals_home    += $partido_info->homegoals;
			$total_conceded_home += $partido_info->awaygoals;
			$total_corners_home  += $partido_info->homecorners;
			$total_total_corners_home += $total_corners; // Total corners in the match
		}
		if ($cur_partido->home == $partido_info->away) {
			$total_goals_home    += $partido_info->awaygoals;
			$total_conceded_home += $partido_info->homegoals;
			$total_corners_home  += $partido_info->awaycorners;
			$total_total_corners_home += $total_corners; // Total corners in the match
		}
		if ($cur_partido->away == $partido_info->home) {
			$total_goals_away    += $partido_info->homegoals;
			$total_conceded_away += $partido_info->awaygoals;
			$total_corners_away  += $partido_info->homecorners;
			$total_total_corners_away += $total_corners; // Total corners in the match
		}
		if ($cur_partido->away == $partido_info->away) {
			$total_goals_away    += $partido_info->awaygoals;
			$total_conceded_away += $partido_info->homegoals;
			$total_corners_away  += $partido_info->awaycorners;
			$total_total_corners_away += $total_corners; // Total corners in the match
		}
		
		//partidos home @home
		if ($cur_partido->home == $partido_info->home) {
			$n_partidos_home_h++;
			$n_cumple_home_marca_home_h            += ($partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_home_superior_1_5_home_h     += ($partido_info->homegoals > 1.5) ? 1 : 0;
			$n_cumple_ambos_marcan_home_h          += ($partido_info->homegoals > 0.5 && $partido_info->awaygoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_home_h += ($total_goles > 1.5) ? 1 : 0;
			$n_cumple_total_corners_masde_6_5_home_h += ($total_corners > 6.5) ? 1 : 0;
			$n_cumple_total_corners_masde_7_5_home_h += ($total_corners > 7.5) ? 1 : 0;
			$n_cumple_total_corners_masde_8_5_home_h += ($total_corners > 8.5) ? 1 : 0;
			$n_cumple_total_corners_masde_9_5_home_h += ($total_corners > 9.5) ? 1 : 0;
			$n_cumple_total_corners_masde_10_5_home_h += ($total_corners > 10.5) ? 1 : 0;
			$n_cumple_total_corners_masde_11_5_home_h += ($total_corners > 11.5) ? 1 : 0;
			$n_cumple_total_corners_masde_12_5_home_h += ($total_corners > 12.5) ? 1 : 0;
			$n_cumple_total_corners_masde_13_5_home_h += ($total_corners > 13.5) ? 1 : 0;
			// INFERIOR corners counting for home @home
			$n_cumple_total_corners_menosde_6_5_home_h += ($total_corners < 6.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_7_5_home_h += ($total_corners < 7.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_8_5_home_h += ($total_corners < 8.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_9_5_home_h += ($total_corners < 9.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_10_5_home_h += ($total_corners < 10.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_11_5_home_h += ($total_corners < 11.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_12_5_home_h += ($total_corners < 12.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_13_5_home_h += ($total_corners < 13.5) ? 1 : 0;
		}
		
		if ($n_partidos_home_h == $n_partidos_p1) {
			$prob_home_marca_p1_home_h            = ceil(($n_cumple_home_marca_home_h * 100) / $n_partidos_p1);
			$prob_home_superior_1_5_p1_home_h     = ceil(($n_cumple_home_superior_1_5_home_h * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_home_h          = ceil(($n_cumple_ambos_marcan_home_h * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_home_h = ceil(($n_cumple_total_goles_masde_1_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_6_5_p1_home_h = ceil(($n_cumple_total_corners_masde_6_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_7_5_p1_home_h = ceil(($n_cumple_total_corners_masde_7_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_8_5_p1_home_h = ceil(($n_cumple_total_corners_masde_8_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_9_5_p1_home_h = ceil(($n_cumple_total_corners_masde_9_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_10_5_p1_home_h = ceil(($n_cumple_total_corners_masde_10_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_11_5_p1_home_h = ceil(($n_cumple_total_corners_masde_11_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_12_5_p1_home_h = ceil(($n_cumple_total_corners_masde_12_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_masde_13_5_p1_home_h = ceil(($n_cumple_total_corners_masde_13_5_home_h * 100) / $n_partidos_p1);
			// INFERIOR corners probability calculations for home @home p1
			$prob_total_corners_menosde_6_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_6_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_7_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_7_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_8_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_8_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_9_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_9_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_10_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_10_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_11_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_11_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_12_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_12_5_home_h * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_13_5_p1_home_h = ceil(($n_cumple_total_corners_menosde_13_5_home_h * 100) / $n_partidos_p1);
		}
		if ($n_partidos_home_h == $n_partidos_p2) {
			$prob_home_marca_p2_home_h            = ceil(($n_cumple_home_marca_home_h * 100) / $n_partidos_p2);
			$prob_home_superior_1_5_p2_home_h     = ceil(($n_cumple_home_superior_1_5_home_h * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_home_h          = ceil(($n_cumple_ambos_marcan_home_h * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_home_h = ceil(($n_cumple_total_goles_masde_1_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_6_5_p2_home_h = ceil(($n_cumple_total_corners_masde_6_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_7_5_p2_home_h = ceil(($n_cumple_total_corners_masde_7_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_8_5_p2_home_h = ceil(($n_cumple_total_corners_masde_8_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_9_5_p2_home_h = ceil(($n_cumple_total_corners_masde_9_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_10_5_p2_home_h = ceil(($n_cumple_total_corners_masde_10_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_11_5_p2_home_h = ceil(($n_cumple_total_corners_masde_11_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_12_5_p2_home_h = ceil(($n_cumple_total_corners_masde_12_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_masde_13_5_p2_home_h = ceil(($n_cumple_total_corners_masde_13_5_home_h * 100) / $n_partidos_p2);
			// INFERIOR corners probability calculations for home @home p2
			$prob_total_corners_menosde_6_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_6_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_7_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_7_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_8_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_8_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_9_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_9_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_10_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_10_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_11_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_11_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_12_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_12_5_home_h * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_13_5_p2_home_h = ceil(($n_cumple_total_corners_menosde_13_5_home_h * 100) / $n_partidos_p2);
		}
		
		//partidos away @away
		if ($cur_partido->away == $partido_info->away) {
			$n_partidos_away_a++;
			$n_cumple_away_marca_away_a            += ($partido_info->awaygoals > 0.5) ? 1 : 0;
			$n_cumple_away_superior_1_5_away_a     += ($partido_info->awaygoals > 1.5) ? 1 : 0;
			$n_cumple_ambos_marcan_away_a          += ($partido_info->awaygoals > 0.5 && $partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_away_a += ($total_goles > 1.5) ? 1 : 0;
			$n_cumple_total_corners_masde_6_5_away_a += ($total_corners > 6.5) ? 1 : 0;
			$n_cumple_total_corners_masde_7_5_away_a += ($total_corners > 7.5) ? 1 : 0;
			$n_cumple_total_corners_masde_8_5_away_a += ($total_corners > 8.5) ? 1 : 0;
			$n_cumple_total_corners_masde_9_5_away_a += ($total_corners > 9.5) ? 1 : 0;
			$n_cumple_total_corners_masde_10_5_away_a += ($total_corners > 10.5) ? 1 : 0;
			$n_cumple_total_corners_masde_11_5_away_a += ($total_corners > 11.5) ? 1 : 0;
			$n_cumple_total_corners_masde_12_5_away_a += ($total_corners > 12.5) ? 1 : 0;
			$n_cumple_total_corners_masde_13_5_away_a += ($total_corners > 13.5) ? 1 : 0;
			// INFERIOR corners counting for away @away
			$n_cumple_total_corners_menosde_6_5_away_a += ($total_corners < 6.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_7_5_away_a += ($total_corners < 7.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_8_5_away_a += ($total_corners < 8.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_9_5_away_a += ($total_corners < 9.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_10_5_away_a += ($total_corners < 10.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_11_5_away_a += ($total_corners < 11.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_12_5_away_a += ($total_corners < 12.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_13_5_away_a += ($total_corners < 13.5) ? 1 : 0;
		}
		
		if ($n_partidos_away_a == $n_partidos_p1) {
			$prob_away_marca_p1_away_a            = ceil(($n_cumple_away_marca_away_a * 100) / $n_partidos_p1);
			$prob_away_superior_1_5_p1_away_a     = ceil(($n_cumple_away_superior_1_5_away_a * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_away_a          = ceil(($n_cumple_ambos_marcan_away_a * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_away_a = ceil(($n_cumple_total_goles_masde_1_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_6_5_p1_away_a = ceil(($n_cumple_total_corners_masde_6_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_7_5_p1_away_a = ceil(($n_cumple_total_corners_masde_7_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_8_5_p1_away_a = ceil(($n_cumple_total_corners_masde_8_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_9_5_p1_away_a = ceil(($n_cumple_total_corners_masde_9_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_10_5_p1_away_a = ceil(($n_cumple_total_corners_masde_10_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_11_5_p1_away_a = ceil(($n_cumple_total_corners_masde_11_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_12_5_p1_away_a = ceil(($n_cumple_total_corners_masde_12_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_masde_13_5_p1_away_a = ceil(($n_cumple_total_corners_masde_13_5_away_a * 100) / $n_partidos_p1);
			// INFERIOR corners probability calculations for away @away p1
			$prob_total_corners_menosde_6_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_6_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_7_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_7_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_8_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_8_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_9_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_9_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_10_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_10_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_11_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_11_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_12_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_12_5_away_a * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_13_5_p1_away_a = ceil(($n_cumple_total_corners_menosde_13_5_away_a * 100) / $n_partidos_p1);
		}
		if ($n_partidos_away_a == $n_partidos_p2) {
			$prob_away_marca_p2_away_a            = ceil(($n_cumple_away_marca_away_a * 100) / $n_partidos_p2);
			$prob_away_superior_1_5_p2_away_a     = ceil(($n_cumple_away_superior_1_5_away_a * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_away_a          = ceil(($n_cumple_ambos_marcan_away_a * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_away_a = ceil(($n_cumple_total_goles_masde_1_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_6_5_p2_away_a = ceil(($n_cumple_total_corners_masde_6_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_7_5_p2_away_a = ceil(($n_cumple_total_corners_masde_7_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_8_5_p2_away_a = ceil(($n_cumple_total_corners_masde_8_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_9_5_p2_away_a = ceil(($n_cumple_total_corners_masde_9_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_10_5_p2_away_a = ceil(($n_cumple_total_corners_masde_10_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_11_5_p2_away_a = ceil(($n_cumple_total_corners_masde_11_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_12_5_p2_away_a = ceil(($n_cumple_total_corners_masde_12_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_masde_13_5_p2_away_a = ceil(($n_cumple_total_corners_masde_13_5_away_a * 100) / $n_partidos_p2);
			// INFERIOR corners probability calculations for away @away p2
			$prob_total_corners_menosde_6_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_6_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_7_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_7_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_8_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_8_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_9_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_9_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_10_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_10_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_11_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_11_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_12_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_12_5_away_a * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_13_5_p2_away_a = ceil(($n_cumple_total_corners_menosde_13_5_away_a * 100) / $n_partidos_p2);
		}
		
		//partidos home
		if ($cur_partido->home == $partido_info->home || $cur_partido->home == $partido_info->away) {
			$n_partidos_home++;
			
			if ($cur_partido->home == $partido_info->home) {
				$n_cumple_home_marca_home        += ($partido_info->homegoals > 0.5) ? 1 : 0;
				$n_cumple_home_superior_1_5_home += ($partido_info->homegoals > 1.5) ? 1 : 0;
			}
			if ($cur_partido->home == $partido_info->away) {
				$n_cumple_home_marca_home        += ($partido_info->awaygoals > 0.5) ? 1 : 0;
				$n_cumple_home_superior_1_5_home += ($partido_info->awaygoals > 1.5) ? 1 : 0;
			}
			
			$n_cumple_ambos_marcan_home          += ($partido_info->awaygoals > 0.5 && $partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_home += ($total_goles > 1.5) ? 1 : 0;
			$n_cumple_total_corners_masde_6_5_home += ($total_corners > 6.5) ? 1 : 0;
			$n_cumple_total_corners_masde_7_5_home += ($total_corners > 7.5) ? 1 : 0;
			$n_cumple_total_corners_masde_8_5_home += ($total_corners > 8.5) ? 1 : 0;
			$n_cumple_total_corners_masde_9_5_home += ($total_corners > 9.5) ? 1 : 0;
			$n_cumple_total_corners_masde_10_5_home += ($total_corners > 10.5) ? 1 : 0;
			$n_cumple_total_corners_masde_11_5_home += ($total_corners > 11.5) ? 1 : 0;
			$n_cumple_total_corners_masde_12_5_home += ($total_corners > 12.5) ? 1 : 0;
			$n_cumple_total_corners_masde_13_5_home += ($total_corners > 13.5) ? 1 : 0;
			// INFERIOR corners counting for home
			$n_cumple_total_corners_menosde_6_5_home += ($total_corners < 6.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_7_5_home += ($total_corners < 7.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_8_5_home += ($total_corners < 8.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_9_5_home += ($total_corners < 9.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_10_5_home += ($total_corners < 10.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_11_5_home += ($total_corners < 11.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_12_5_home += ($total_corners < 12.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_13_5_home += ($total_corners < 13.5) ? 1 : 0;
		}
		
		if ($n_partidos_home == $n_partidos_p1) {
			$prob_home_marca_p1_home            = ceil(($n_cumple_home_marca_home * 100) / $n_partidos_p1);
			$prob_home_superior_1_5_p1_home     = ceil(($n_cumple_home_superior_1_5_home * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_home          = ceil(($n_cumple_ambos_marcan_home * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_home = ceil(($n_cumple_total_goles_masde_1_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_6_5_p1_home = ceil(($n_cumple_total_corners_masde_6_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_7_5_p1_home = ceil(($n_cumple_total_corners_masde_7_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_8_5_p1_home = ceil(($n_cumple_total_corners_masde_8_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_9_5_p1_home = ceil(($n_cumple_total_corners_masde_9_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_10_5_p1_home = ceil(($n_cumple_total_corners_masde_10_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_11_5_p1_home = ceil(($n_cumple_total_corners_masde_11_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_12_5_p1_home = ceil(($n_cumple_total_corners_masde_12_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_masde_13_5_p1_home = ceil(($n_cumple_total_corners_masde_13_5_home * 100) / $n_partidos_p1);
			// INFERIOR corners probability calculations for home p1
			$prob_total_corners_menosde_6_5_p1_home = ceil(($n_cumple_total_corners_menosde_6_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_7_5_p1_home = ceil(($n_cumple_total_corners_menosde_7_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_8_5_p1_home = ceil(($n_cumple_total_corners_menosde_8_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_9_5_p1_home = ceil(($n_cumple_total_corners_menosde_9_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_10_5_p1_home = ceil(($n_cumple_total_corners_menosde_10_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_11_5_p1_home = ceil(($n_cumple_total_corners_menosde_11_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_12_5_p1_home = ceil(($n_cumple_total_corners_menosde_12_5_home * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_13_5_p1_home = ceil(($n_cumple_total_corners_menosde_13_5_home * 100) / $n_partidos_p1);
			$avg_goals_p1_home                  = round($total_goals_home / $n_partidos_p1, 2);
			$avg_conceded_p1_home               = round($total_conceded_home / $n_partidos_p1, 2);
			$avg_corners_p1_home                = round($total_corners_home / $n_partidos_p1, 2);
			$avg_total_corners_p1_home          = round($total_total_corners_home / $n_partidos_p1, 2);
		}
		if ($n_partidos_home == $n_partidos_p2) {
			$prob_home_marca_p2_home            = ceil(($n_cumple_home_marca_home * 100) / $n_partidos_p2);
			$prob_home_superior_1_5_p2_home     = ceil(($n_cumple_home_superior_1_5_home * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_home          = ceil(($n_cumple_ambos_marcan_home * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_home = ceil(($n_cumple_total_goles_masde_1_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_6_5_p2_home = ceil(($n_cumple_total_corners_masde_6_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_7_5_p2_home = ceil(($n_cumple_total_corners_masde_7_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_8_5_p2_home = ceil(($n_cumple_total_corners_masde_8_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_9_5_p2_home = ceil(($n_cumple_total_corners_masde_9_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_10_5_p2_home = ceil(($n_cumple_total_corners_masde_10_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_11_5_p2_home = ceil(($n_cumple_total_corners_masde_11_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_12_5_p2_home = ceil(($n_cumple_total_corners_masde_12_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_masde_13_5_p2_home = ceil(($n_cumple_total_corners_masde_13_5_home * 100) / $n_partidos_p2);
			// INFERIOR corners probability calculations for home p2
			$prob_total_corners_menosde_6_5_p2_home = ceil(($n_cumple_total_corners_menosde_6_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_7_5_p2_home = ceil(($n_cumple_total_corners_menosde_7_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_8_5_p2_home = ceil(($n_cumple_total_corners_menosde_8_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_9_5_p2_home = ceil(($n_cumple_total_corners_menosde_9_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_10_5_p2_home = ceil(($n_cumple_total_corners_menosde_10_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_11_5_p2_home = ceil(($n_cumple_total_corners_menosde_11_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_12_5_p2_home = ceil(($n_cumple_total_corners_menosde_12_5_home * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_13_5_p2_home = ceil(($n_cumple_total_corners_menosde_13_5_home * 100) / $n_partidos_p2);
			$avg_goals_p2_home                  = round($total_goals_home / $n_partidos_p2, 2);
			$avg_conceded_p2_home               = round($total_conceded_home / $n_partidos_p2, 2);
			$avg_corners_p2_home                = round($total_corners_home / $n_partidos_p2, 2);
			$avg_total_corners_p2_home          = round($total_total_corners_home / $n_partidos_p2, 2);
		}
		
		//partidos away
		if ($cur_partido->away == $partido_info->home || $cur_partido->away == $partido_info->away) {
			$n_partidos_away++;
			
			if ($cur_partido->away == $partido_info->home) {
				$n_cumple_away_marca_away        += ($partido_info->homegoals > 0.5) ? 1 : 0;
				$n_cumple_away_superior_1_5_away += ($partido_info->homegoals > 1.5) ? 1 : 0;
			}
			if ($cur_partido->away == $partido_info->away) {
				$n_cumple_away_marca_away        += ($partido_info->awaygoals > 0.5) ? 1 : 0;
				$n_cumple_away_superior_1_5_away += ($partido_info->awaygoals > 1.5) ? 1 : 0;
			}
			
			$n_cumple_ambos_marcan_away          += ($partido_info->awaygoals > 0.5 && $partido_info->homegoals > 0.5) ? 1 : 0;
			$n_cumple_total_goles_masde_1_5_away += ($total_goles > 1.5) ? 1 : 0;
			$n_cumple_total_corners_masde_6_5_away += ($total_corners > 6.5) ? 1 : 0;
			$n_cumple_total_corners_masde_7_5_away += ($total_corners > 7.5) ? 1 : 0;
			$n_cumple_total_corners_masde_8_5_away += ($total_corners > 8.5) ? 1 : 0;
			$n_cumple_total_corners_masde_9_5_away += ($total_corners > 9.5) ? 1 : 0;
			$n_cumple_total_corners_masde_10_5_away += ($total_corners > 10.5) ? 1 : 0;
			$n_cumple_total_corners_masde_11_5_away += ($total_corners > 11.5) ? 1 : 0;
			$n_cumple_total_corners_masde_12_5_away += ($total_corners > 12.5) ? 1 : 0;
			$n_cumple_total_corners_masde_13_5_away += ($total_corners > 13.5) ? 1 : 0;
			// INFERIOR corners counting for away
			$n_cumple_total_corners_menosde_6_5_away += ($total_corners < 6.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_7_5_away += ($total_corners < 7.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_8_5_away += ($total_corners < 8.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_9_5_away += ($total_corners < 9.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_10_5_away += ($total_corners < 10.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_11_5_away += ($total_corners < 11.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_12_5_away += ($total_corners < 12.5) ? 1 : 0;
			$n_cumple_total_corners_menosde_13_5_away += ($total_corners < 13.5) ? 1 : 0;
		}
		
		if ($n_partidos_away == $n_partidos_p1) {
			$prob_away_marca_p1_away            = ceil(($n_cumple_away_marca_away * 100) / $n_partidos_p1);
			$prob_away_superior_1_5_p1_away     = ceil(($n_cumple_away_superior_1_5_away * 100) / $n_partidos_p1);
			$prob_ambos_marcan_p1_away          = ceil(($n_cumple_ambos_marcan_away * 100) / $n_partidos_p1);
			$prob_total_goles_masde_1_5_p1_away = ceil(($n_cumple_total_goles_masde_1_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_6_5_p1_away = ceil(($n_cumple_total_corners_masde_6_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_7_5_p1_away = ceil(($n_cumple_total_corners_masde_7_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_8_5_p1_away = ceil(($n_cumple_total_corners_masde_8_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_9_5_p1_away = ceil(($n_cumple_total_corners_masde_9_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_10_5_p1_away = ceil(($n_cumple_total_corners_masde_10_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_11_5_p1_away = ceil(($n_cumple_total_corners_masde_11_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_12_5_p1_away = ceil(($n_cumple_total_corners_masde_12_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_masde_13_5_p1_away = ceil(($n_cumple_total_corners_masde_13_5_away * 100) / $n_partidos_p1);
			// INFERIOR corners probability calculations for away p1
			$prob_total_corners_menosde_6_5_p1_away = ceil(($n_cumple_total_corners_menosde_6_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_7_5_p1_away = ceil(($n_cumple_total_corners_menosde_7_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_8_5_p1_away = ceil(($n_cumple_total_corners_menosde_8_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_9_5_p1_away = ceil(($n_cumple_total_corners_menosde_9_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_10_5_p1_away = ceil(($n_cumple_total_corners_menosde_10_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_11_5_p1_away = ceil(($n_cumple_total_corners_menosde_11_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_12_5_p1_away = ceil(($n_cumple_total_corners_menosde_12_5_away * 100) / $n_partidos_p1);
			$prob_total_corners_menosde_13_5_p1_away = ceil(($n_cumple_total_corners_menosde_13_5_away * 100) / $n_partidos_p1);
			$avg_goals_p1_away                  = round($total_goals_away / $n_partidos_p1, 2);
			$avg_conceded_p1_away               = round($total_conceded_away / $n_partidos_p1, 2);
			$avg_corners_p1_away                = round($total_corners_away / $n_partidos_p1, 2);
			$avg_total_corners_p1_away          = round($total_total_corners_away / $n_partidos_p1, 2);
		}
		if ($n_partidos_away == $n_partidos_p2) {
			$prob_away_marca_p2_away            = ceil(($n_cumple_away_marca_away * 100) / $n_partidos_p2);
			$prob_away_superior_1_5_p2_away     = ceil(($n_cumple_away_superior_1_5_away * 100) / $n_partidos_p2);
			$prob_ambos_marcan_p2_away          = ceil(($n_cumple_ambos_marcan_away * 100) / $n_partidos_p2);
			$prob_total_goles_masde_1_5_p2_away = ceil(($n_cumple_total_goles_masde_1_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_6_5_p2_away = ceil(($n_cumple_total_corners_masde_6_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_7_5_p2_away = ceil(($n_cumple_total_corners_masde_7_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_8_5_p2_away = ceil(($n_cumple_total_corners_masde_8_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_9_5_p2_away = ceil(($n_cumple_total_corners_masde_9_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_10_5_p2_away = ceil(($n_cumple_total_corners_masde_10_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_11_5_p2_away = ceil(($n_cumple_total_corners_masde_11_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_12_5_p2_away = ceil(($n_cumple_total_corners_masde_12_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_masde_13_5_p2_away = ceil(($n_cumple_total_corners_masde_13_5_away * 100) / $n_partidos_p2);
			// INFERIOR corners probability calculations for away p2
			$prob_total_corners_menosde_6_5_p2_away = ceil(($n_cumple_total_corners_menosde_6_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_7_5_p2_away = ceil(($n_cumple_total_corners_menosde_7_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_8_5_p2_away = ceil(($n_cumple_total_corners_menosde_8_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_9_5_p2_away = ceil(($n_cumple_total_corners_menosde_9_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_10_5_p2_away = ceil(($n_cumple_total_corners_menosde_10_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_11_5_p2_away = ceil(($n_cumple_total_corners_menosde_11_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_12_5_p2_away = ceil(($n_cumple_total_corners_menosde_12_5_away * 100) / $n_partidos_p2);
			$prob_total_corners_menosde_13_5_p2_away = ceil(($n_cumple_total_corners_menosde_13_5_away * 100) / $n_partidos_p2);
			$avg_goals_p2_away                  = round($total_goals_away / $n_partidos_p2, 2);
			$avg_conceded_p2_away               = round($total_conceded_away / $n_partidos_p2, 2);
			$avg_corners_p2_away                = round($total_corners_away / $n_partidos_p2, 2);
			$avg_total_corners_p2_away          = round($total_total_corners_away / $n_partidos_p2, 2);
		}
	}
} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try
#region sub_agregar_prob_total_goles_masde_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_total_goles_masde_1_5'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::TOTAL_GOLES_MASDE_1_5;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_total_goles_masde_1_5_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_total_goles_masde_1_5_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_total_goles_masde_1_5_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_total_goles_masde_1_5_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_total_goles_masde_1_5_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_total_goles_masde_1_5_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_total_goles_masde_1_5_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_total_goles_masde_1_5_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_1_5']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de total goles mas de 1.5 han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_total_goles_masde_1_5
#region sub_agregar_prob_home_marca
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_home_marca'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::HOME_MARCA;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_home_marca_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_home_marca_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = -1;
		$new_partido_probabilidad->p1_away_a            = -1;
		$new_partido_probabilidad->p1_away              = -1;
		$new_partido_probabilidad->p1_avg_away          = -1;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_home_marca_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_home_marca_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = -1;
		$new_partido_probabilidad->p2_away_a            = -1;
		$new_partido_probabilidad->p2_away              = -1;
		$new_partido_probabilidad->p2_avg_away          = -1;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_home_marca']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de home marca han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_home_marca
#region sub_agregar_prob_home_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_home_superior_1_5'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::HOME_SUPERIOR_1_5;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_home_superior_1_5_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_home_superior_1_5_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = -1;
		$new_partido_probabilidad->p1_away_a            = -1;
		$new_partido_probabilidad->p1_away              = -1;
		$new_partido_probabilidad->p1_avg_away          = -1;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_home_superior_1_5_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_home_superior_1_5_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = -1;
		$new_partido_probabilidad->p2_away_a            = -1;
		$new_partido_probabilidad->p2_away              = -1;
		$new_partido_probabilidad->p2_avg_away          = -1;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_home_superior_1_5']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de home SUPERIOR 1.5 han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_home_superior_1_5
#region sub_agregar_prob_away_marca
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_away_marca'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::AWAY_MARCA;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = -1;
		$new_partido_probabilidad->p1_home              = -1;
		$new_partido_probabilidad->p1_avg_home          = -1;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_away_marca_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_away_marca_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = -1;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = -1;
		$new_partido_probabilidad->p2_home              = -1;
		$new_partido_probabilidad->p2_avg_home          = -1;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_away_marca_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_away_marca_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = -1;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_away_marca']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de away marca han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_away_marca
#region sub_agregar_prob_away_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_away_superior_1_5'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::AWAY_SUPERIOR_1_5;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = -1;
		$new_partido_probabilidad->p1_home              = -1;
		$new_partido_probabilidad->p1_avg_home          = -1;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_away_superior_1_5_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_away_superior_1_5_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = -1;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = -1;
		$new_partido_probabilidad->p2_home              = -1;
		$new_partido_probabilidad->p2_avg_home          = -1;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_away_superior_1_5_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_away_superior_1_5_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = -1;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_away_superior_1_5']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de away SUPERIOR 1.5 han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_away_superior_1_5
#region sub_agregar_prob_ambos_marcan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_agregar_prob_ambos_marcan'])) {
	try {
		$new_partido_probabilidad                       = new PartidoProbabilidad();
		$new_partido_probabilidad->partido->id          = $id_partido;
		$new_partido_probabilidad->tipo_apuesta         = PartidoProbabilidad::AMBOS_MARCAN;
		$new_partido_probabilidad->p1_same_fixture      = -1;
		$new_partido_probabilidad->p1_home_h            = $prob_ambos_marcan_p1_home_h;
		$new_partido_probabilidad->p1_home              = $prob_ambos_marcan_p1_home;
		$new_partido_probabilidad->p1_avg_home          = $avg_goals_p1_home;
		$new_partido_probabilidad->p1_avg_conceded_home = $avg_conceded_p1_home;
		$new_partido_probabilidad->p1_away_a            = $prob_ambos_marcan_p1_away_a;
		$new_partido_probabilidad->p1_away              = $prob_ambos_marcan_p1_away;
		$new_partido_probabilidad->p1_avg_away          = $avg_goals_p1_away;
		$new_partido_probabilidad->p1_avg_conceded_away = $avg_conceded_p1_away;
		$new_partido_probabilidad->p2_same_fixture      = -1;
		$new_partido_probabilidad->p2_home_h            = $prob_ambos_marcan_p2_home_h;
		$new_partido_probabilidad->p2_home              = $prob_ambos_marcan_p2_home;
		$new_partido_probabilidad->p2_avg_home          = $avg_goals_p2_home;
		$new_partido_probabilidad->p2_avg_conceded_home = $avg_conceded_p2_home;
		$new_partido_probabilidad->p2_away_a            = $prob_ambos_marcan_p2_away_a;
		$new_partido_probabilidad->p2_away              = $prob_ambos_marcan_p2_away;
		$new_partido_probabilidad->p2_avg_away          = $avg_goals_p2_away;
		$new_partido_probabilidad->p2_avg_conceded_away = $avg_conceded_p2_away;
		$new_partido_probabilidad->porc_acierto         = limpiar_datos($_POST['porc_acierto_ambos_marcan']);
		$new_partido_probabilidad->agregar($conexion);
		
		$success_display = 'show';
		$success_text    = 'Las probabilidades de ambos marcan han sido guardadas.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_agregar_prob_ambos_marcan
#region ajax_add_bet_home_superior_0_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_home_superior_0_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Additional debug logging
		error_log("AJAX Betting - Session id_partido: " . var_export($id_partido, true));
		error_log("AJAX Betting - Session contents: " . var_export($_SESSION, true));

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena(47)); // Set to 47 (hardcoded)

		// Debug log for partido ID (can be removed after testing)
		error_log("AJAX Betting - id_partido value: " . var_export($id_partido, true));

		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_home_superior_0_5

#region ajax_add_bet_total_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_total_superior_1_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 48;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_total_superior_1_5

#region ajax_add_bet_home_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_home_superior_1_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 49;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_home_superior_1_5

#region ajax_add_bet_away_superior_0_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_away_superior_0_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 50;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_away_superior_0_5

#region ajax_add_bet_away_superior_1_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_away_superior_1_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 51;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_away_superior_1_5

#region ajax_add_bet_ambos_marcan
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_ambos_marcan'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 52;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_ambos_marcan

#region ajax_add_bet_corner_6_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_6_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 53;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_6_5

#region ajax_add_bet_corner_7_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_7_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 54;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_7_5

#region ajax_add_bet_corner_8_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_8_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 55;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_8_5

#region ajax_add_bet_corner_9_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_9_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 56;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_9_5

#region ajax_add_bet_corner_10_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_10_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 57;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_10_5

#region ajax_add_bet_corner_11_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_11_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 58;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_11_5

#region ajax_add_bet_corner_12_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_12_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 59;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_12_5

#region ajax_add_bet_corner_13_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_13_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 60;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_13_5

#region ajax_add_bet_corner_inferior_6_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_6_5'])) {
	try {
		// Validate AJAX request
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}

		// Get partido ID from session (same logic as GET method)
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}

		// Validate partido ID
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}

		// Get and validate input data
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 61;

		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}

		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}

		$conexion->beginTransaction();

		// Create PartidoBetDetalle record
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null); // Set to NULL as required
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);

		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}

		// Create PartidoBetCriterio record with all criteria
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());

		// Map criteria to the criterio fields (up to 12 fields available)
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break; // Maximum 12 criterios supported

			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";

			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}

			$criterioIndex++;
		}

		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}

		$conexion->commit();

		// Return success response
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();

	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}

		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_6_5

#region ajax_add_bet_corner_inferior_7_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_7_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 62;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_7_5

#region ajax_add_bet_corner_inferior_8_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_8_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 63;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_8_5

#region ajax_add_bet_corner_inferior_9_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_9_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 64;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_9_5

#region ajax_add_bet_corner_inferior_10_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_10_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 65;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_10_5

#region ajax_add_bet_corner_inferior_11_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_11_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 66;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_11_5

#region ajax_add_bet_corner_inferior_12_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_12_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 67;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_12_5

#region ajax_add_bet_corner_inferior_13_5
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajax_add_bet_corner_inferior_13_5'])) {
	try {
		if (!isset($_POST['ajax']) || $_POST['ajax'] != 1) {
			throw new Exception('Invalid request');
		}
		if (isset($_SESSION['id_partido'])) {
			$id_partido = $_SESSION['id_partido'];
		} else {
			throw new Exception('No se encontró el ID del partido en la sesión');
		}
		if (empty($id_partido) || trim($id_partido) === '') {
			throw new Exception('ID del partido no válido - valor recibido: ' . var_export($id_partido, true));
		}
		$cuota = limpiar_datos($_POST['cuota']);
		$criterios = $_POST['criterios'] ?? [];
		$bet_type_code = $_POST['bet_type_code'] ?? 68;
		if (empty($cuota) || !is_numeric($cuota) || $cuota <= 0) {
			throw new Exception('Debe especificar una cuota válida mayor que cero');
		}
		if (empty($criterios) || !is_array($criterios)) {
			throw new Exception('No se encontraron criterios para guardar');
		}
		$conexion->beginTransaction();
		$partidoBetDetalle = new \App\classes\PartidoBetDetalle();
		$partidoBetDetalle->setIdPartidoBet(null);
		$partidoBetDetalle->setIdApuestaTipo(desordena($bet_type_code));
		$partidoBetDetalle->setIdPartido($id_partido);
		$partidoBetDetalle->setCuota((float)$cuota);
		if (!$partidoBetDetalle->guardar($conexion)) {
			throw new Exception('Error al guardar el detalle de la apuesta');
		}
		$partidoBetCriterio = new \App\classes\PartidoBetCriterio();
		$partidoBetCriterio->setIdPartidoBetDetalle($partidoBetDetalle->getId());
		$criterioIndex = 1;
		foreach ($criterios as $criterio) {
			if ($criterioIndex > 12) break;
			$nombreMethod = "setCriterioNombre$criterioIndex";
			$valorMethod = "setCriterioValor$criterioIndex";
			if (isset($criterio['nombre']) && isset($criterio['valor'])) {
				$partidoBetCriterio->$nombreMethod($criterio['nombre']);
				$partidoBetCriterio->$valorMethod((float)$criterio['valor']);
			}
			$criterioIndex++;
		}
		if (!$partidoBetCriterio->guardar($conexion)) {
			throw new Exception('Error al guardar los criterios de la apuesta');
		}
		$conexion->commit();
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'success',
			'message' => 'Apuesta agregada exitosamente',
			'data' => [
				'id_partido_bet_detalle' => $partidoBetDetalle->getId(),
				'cuota' => $cuota,
				'criterios_count' => count($criterios)
			]
		]);
		exit();
	} catch (Exception $e) {
		if ($conexion->inTransaction()) {
			$conexion->rollback();
		}
		header('Content-Type: application/json');
		echo json_encode([
			'status' => 'error',
			'message' => $e->getMessage()
		]);
		exit();
	}
}
#endregion ajax_add_bet_corner_inferior_13_5

#region sub_create_tournament
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_create_tournament'])) {
	try {
		$tournament_name = limpiar_datos($_POST['tournament_name']);

		// Create new Pais (tournament)
		$newpais = new Pais();
		$newpais->nombre = $tournament_name;
		$newpais->perc_penalidad = 0; // Default penalty percentage
		$newpais->add($conexion);

		// Add the new tournament to the current match (check if not already exists)
		if (PartidoTorneo::existe_by_idpais_n_idpartido($newpais->id, $id_partido, $conexion) == 0) {
			$new_partido_torneo = new PartidoTorneo();
			$new_partido_torneo->partido->id = $id_partido;
			$new_partido_torneo->pais->id = $newpais->id;
			$new_partido_torneo->add($conexion);
		}

		// Preserve the id_partido in session for page reload
		$_SESSION['id_partido'] = $id_partido;

		$success_display = 'show';
		$success_text = 'Tournament created and added successfully.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_create_tournament
#region sub_edit_tournament
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_edit_tournament'])) {
	try {
		$tournament_id = limpiar_datos($_POST['tournament_id']);
		$tournament_name = limpiar_datos($_POST['tournament_name']);

		// Update the Pais (tournament)
		$modpais = new Pais();
		$modpais->id = $tournament_id;
		$modpais->nombre = $tournament_name;
		// Get current penalty percentage to preserve it
		$current_pais = Pais::get($tournament_id, $conexion);
		$modpais->perc_penalidad = $current_pais->perc_penalidad;
		$modpais->modify($conexion);

		// Preserve the id_partido in session for page reload
		$_SESSION['id_partido'] = $id_partido;

		$success_display = 'show';
		$success_text = 'Tournament updated successfully.';

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text = $e->getMessage();
	}
}
#endregion sub_edit_tournament

/**
 * Format partido fecha and hora militar to display format with AM/PM in Bogotá timezone
 * @param object $partido The partido object with fecha and horamilitar properties
 * @return string Formatted date and time string (e.g., "2024-01-15 2:30 PM")
 */
function formatPartidoFechaHora($partido) {
	if (empty($partido->fecha) || empty($partido->horamilitar)) {
		return '';
	}

	$hora24 = format_date_to_hora_24_format_HHMM($partido->horamilitar); // e.g., 14:30
	$hora12 = $hora24; // fallback

	try {
		$dt = DateTime::createFromFormat('H:i', $hora24, new DateTimeZone('America/Bogota'));
		if ($dt instanceof DateTime) {
			$hora12 = $dt->format('g:i A'); // e.g., 2:30 PM
		}
	} catch (Exception $e) {
		// keep fallback
	}

	return $partido->fecha . ' ' . $hora12;
}

require_once __ROOT__ . '/views/epartido_probabilidades.view.php';

?>


