<?php

require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/pais.php';

class PartidoTorneo
{
	public string  $id;
	public Partido $partido;
	public Pais    $pais;
	public int     $estado;
	public string  $seasons;
	public ?string $season;
	public ?string $fecha_upload;
	private string $bd_table      = 'partidos_torneos';
	private string $bd_alias      = 'partor';
	private string $bd_id         = 'id_partido_torneo';
	private string $bd_idpartido  = 'id_partido';
	private string $bd_idpais     = 'id_pais';
	private string $bd_nombrepais = 'nombre_pais';
	private string $bd_estado     = 'estado';
	private string $bd_season     = 'season';
	
	function __construct()
	{
		$this->id           = '';
		$this->partido      = new Partido;
		$this->partido->id  = '';
		$this->pais         = new Pais;
		$this->pais->id     = '';
		$this->estado       = 0;
		$this->seasons      = '';
		$this->season       = '';
		$this->fecha_upload = '';
	}
	
	/**
	 * @param $resultado
	 *
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado, $conexion): self
	{
		try {
			$cq = new self;
			
			$objeto               = new self;
			$objeto->id           = desordena($resultado[$cq->bd_id]);
			$objeto->partido      = new Partido;
			$objeto->partido->id  = desordena($resultado[$cq->bd_idpartido]);
			$objeto->pais         = new Pais;
			$objeto->pais->id     = desordena($resultado[$cq->bd_idpais]);
			$objeto->pais->nombre = (isset($resultado[$cq->bd_nombrepais])) ? $resultado[$cq->bd_nombrepais] : "";
			$objeto->estado       = $resultado[$cq->bd_estado];
			$objeto->seasons      = PartidoInfo::get_seasons_byidpais($objeto->pais->id, $conexion);
			$objeto->fecha_upload = $resultado['fecha_upload'] ?? '';
			$objeto->season       = $resultado['season'] ?? '';
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, $conexion): self
	{
		try {
			$cq  = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado, $conexion);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_by_idpais_n_idpartido($idpais, $idpartido, $conexion): self
	{
		try {
			$cqpartidotorneo  = new self;
			$cqapartidotorneo = $cqpartidotorneo->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqapartidotorneo.* ";
			$query .= "FROM $cqpartidotorneo->bd_table $cqapartidotorneo ";
			$query .= "WHERE ";
			$query .= "  $cqapartidotorneo.$cqpartidotorneo->bd_idpais = :$cqpartidotorneo->bd_idpais ";
			$query .= "  AND $cqapartidotorneo.$cqpartidotorneo->bd_idpartido = :$cqpartidotorneo->bd_idpartido ";
			$query .= "  AND $cqapartidotorneo.$cqpartidotorneo->bd_estado = :$cqpartidotorneo->bd_estado ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cqpartidotorneo->bd_idpais", ordena($idpais));
			$statement->bindValue(":$cqpartidotorneo->bd_idpartido", ordena($idpartido));
			$statement->bindValue(":$cqpartidotorneo->bd_estado", 1);
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado, $conexion);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function existe_by_idpais_n_idpartido($idpais, $idpartido, $conexion): int
	{
		try {
			$partidotorneo = self::get_by_idpais_n_idpartido($idpais, $idpartido, $conexion);

			if (empty($partidotorneo->id)) {
				return 0;
			} else {
				return 1;
			}

		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	/**
	 * Check if tournament exists for a match considering season
	 * @throws Exception
	 */
	public static function existe_by_idpais_n_idpartido_n_season($idpais, $idpartido, $season, $conexion): int
	{
		try {
			$partidotorneo = self::get_by_idpais_n_idpartido_n_season($idpais, $idpartido, $season, $conexion);

			if (empty($partidotorneo->id)) {
				return 0;
			} else {
				return 1;
			}

		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	/**
	 * Get tournament by pais, partido and season
	 * @throws Exception
	 */
	public static function get_by_idpais_n_idpartido_n_season($idpais, $idpartido, $season, $conexion): self
	{
		try {
			$cqpartidotorneo  = new self;
			$cqapartidotorneo = $cqpartidotorneo->bd_alias;

			// Check if season column exists in the database
			$columnExists = self::checkSeasonColumnExists($conexion);

			$query = "SELECT ";
			$query .= "  $cqapartidotorneo.* ";
			$query .= "FROM $cqpartidotorneo->bd_table $cqapartidotorneo ";
			$query .= "WHERE ";
			$query .= "  $cqapartidotorneo.$cqpartidotorneo->bd_idpais = :$cqpartidotorneo->bd_idpais ";
			$query .= "  AND $cqapartidotorneo.$cqpartidotorneo->bd_idpartido = :$cqpartidotorneo->bd_idpartido ";
			$query .= "  AND $cqapartidotorneo.$cqpartidotorneo->bd_estado = :$cqpartidotorneo->bd_estado ";

			// Only add season condition if column exists
			if ($columnExists) {
				// Add season condition - handle both empty season and specific season
				if (empty($season)) {
					$query .= "  AND ($cqapartidotorneo.season IS NULL OR $cqapartidotorneo.season = '') ";
				} else {
					$query .= "  AND $cqapartidotorneo.season = :season ";
				}
			}

			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cqpartidotorneo->bd_idpais", ordena($idpais));
			$statement->bindValue(":$cqpartidotorneo->bd_idpartido", ordena($idpartido));
			$statement->bindValue(":$cqpartidotorneo->bd_estado", 1);

			if ($columnExists && !empty($season)) {
				$statement->bindValue(":season", $season);
			}

			$statement->execute();
			$resultado = $statement->fetch();

			if ($resultado) {
				return self::construct($resultado, $conexion);

			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}

	/**
	 * Check if season column exists in partidos_torneos table
	 * @throws Exception
	 */
	private static function checkSeasonColumnExists($conexion): bool
	{
		try {
			$query = "SHOW COLUMNS FROM partidos_torneos LIKE 'season'";
			$statement = $conexion->prepare($query);
			$statement->execute();
			$result = $statement->fetch();

			return $result !== false;
		} catch (Exception $e) {
			// If there's an error checking, assume column doesn't exist
			return false;
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function getList($idpartido, $conexion): array
	{
		try {
			$query = <<<SQL
						SELECT
							 pt.*
							,p.nombre AS 'nombre_pais'
						FROM partidos_torneos pt
						INNER JOIN paises p ON pt.id_pais = p.id_pais
						WHERE
							pt.estado = 1
							AND pt.id_partido = :id_partido
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_partido", ordena($idpartido));
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado, $conexion);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	
	/**
	 * @throws Exception
	 */
	public static function get_list_grouped_w_info($id_partido, $conexion): array
	{
		try {
			$query = <<<SQL
						SELECT
							 pt.*
							,p.nombre AS 'nombre_pais'
							,IFNULL(pi.season, '') AS season
							,IFNULL(pi.fecha_upload, '') AS fecha_upload
						FROM partidos_torneos pt
						INNER JOIN paises p ON pt.id_pais = p.id_pais
						LEFT JOIN partidos_info pi ON pt.id_pais = pi.id_pais AND pi.status = 'COMPLETE'
						WHERE
							pt.estado = 1
							AND pt.id_partido = :id_partido
						GROUP BY
							 pt.id_pais
							,pi.season
						ORDER BY
							 p.nombre
							,pi.season DESC
						SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_partido", ordena($id_partido));
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado, $conexion);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function add($conexion): void
	{
		try {
			$this->validateData($conexion);
			
			$cq = new self;
			
			// Check if season column exists in the database
			$columnExists = self::checkSeasonColumnExists($conexion);

			// Build query dynamically based on whether season is provided and column exists
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "  $cq->bd_idpartido ";
			$query .= "  ,$cq->bd_idpais ";

			if (!empty($this->season) && $columnExists) {
				$query .= "  ,$cq->bd_season ";
			}

			$query .= ") VALUES (";
			$query .= "  :$cq->bd_idpartido ";
			$query .= "  ,:$cq->bd_idpais ";

			if (!empty($this->season) && $columnExists) {
				$query .= "  ,:$cq->bd_season ";
			}

			$query .= ") ";

			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_idpartido", ordena($this->partido->id));
			$statement->bindValue(":$cq->bd_idpais", ordena($this->pais->id));

			if (!empty($this->season) && $columnExists) {
				$statement->bindValue(":$cq->bd_season", $this->season);
			}

			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function delete($id, $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = :$cq->bd_estado ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_estado", 0);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validateData($conexion): void
	{
		try {
			if (empty($this->pais->id)) {
				validar_textovacio($this->pais->nombre, "Debe especificar el pais");

				$this->pais->id = Pais::getByNombre($this->pais->nombre, $conexion);
			}

			// Check if season column exists
			$columnExists = self::checkSeasonColumnExists($conexion);

			// Use season-aware validation if season is provided and column exists, otherwise use original validation
			if (!empty($this->season) && $columnExists) {
				if (self::existe_by_idpais_n_idpartido_n_season($this->pais->id, $this->partido->id, $this->season, $conexion) == 1) {
					throw new Exception('El torneo seleccionado con esta temporada ya esta asociado a este partido.');
				}
			} else {
				// Original validation for backward compatibility
				if (self::existe_by_idpais_n_idpartido($this->pais->id, $this->partido->id, $conexion) == 1) {
					throw new Exception('El torneo seleccionado ya esta asociado a este partido.');
				}
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>